// import { storeRegister } from 'src/store/storeRegister'
import React from 'react'
import { Modal, Tooltip, message } from 'antd'
import JSONBigFn from 'json-bigint'
// import { LogoutMessage } from 'src/features/LogoutMessage'
// import { ProviderStore } from 'src'
import { setIsLoggedIn } from 'src/appPages/login/loginSlice';
import { store } from 'src/store/index'
import { UAParser } from 'ua-parser-js'; 
import SparkMD5 from 'spark-md5'
import { CloseCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons'
import styles from './index.module.scss'
import i18n from 'i18next';

export const RPC_ERROR_CODE = 2006
const SDT_NODE_ERROR_CODE = -400013
export const RPC_ERROR_MESSAGE = 'rpc-error-2006'

const JSONBig = JSONBigFn({ storeAsString: true })

/**
 * key => message
 * value => 是否阻止 message 显示
 */
const blockMap = new Map<string, boolean>()

const throttleErrorMessage = (msg: string) => {
  const block = blockMap.get(msg)
  // 当前处于 block 状态，直接略过
  if (block) return
  // 当前非 block 状态，显示 error message 并设置 block 状态
  message.error(msg)
  blockMap.set(msg, true)
  // 1s 后关闭 block 状态
  setTimeout(() => {
    blockMap.set(msg, false)
  }, 1000)
}

// for system_data_operate sdt node expansion API 
const throttleErrorMessageSdt = (msg: string) => {
  const block = blockMap.get(msg)
  // 当前处于 block 状态，直接略过
  if (block) return
  // 当前非 block 状态，显示 error message 并设置 block 状态
  const key = 'sdt-node-error' + new Date().getTime();
  message.error({
    duration: 25,
    key,
    type: 'error',
    content: <span className={styles.errorSdtNode}>
      {msg}
      <Tooltip title={String(i18n.t("closeHint"))} mouseEnterDelay={1}>
        {/* @ts-ignore */}
        <CloseCircleFilled
          className={styles.errorCloseIcon}
          onClick={() => {
            // destory会关闭所有提示，所以只能通过key和时间戳结合css设置来关闭指定提示
            // message.destroy()
            message.error({
              className: styles.errorClose,
              key,
              type: 'error',
              duration: 0,
              content: <></>
            });
          }} />
      </Tooltip>
    </span>
  })

  blockMap.set(msg, true)
  // 1s 后关闭 block 状态
  setTimeout(() => {
    blockMap.set(msg, false)
  }, 1000)
}


export const throttleWarnMessage = (msg: string) => {
  const block = blockMap.get(msg)
  // 当前处于 block 状态，直接略过
  if (block) return
  // 当前非 block 状态，显示 error message 并设置 block 状态
  message.warn(msg)
  blockMap.set(msg, true)
  // 1s 后关闭 block 状态
  setTimeout(() => {
    blockMap.set(msg, false)
  }, 1000)
}

export const handleHttpError = (response: Response, info?: string) => {
  if (response.ok) return
  if (response.status === 401) {
    // const persistor = storeRegister.getPersistor()
    // persistor && persistor.purge()
    // throttleErrorMessage('登录失效')
    // message.destroy()
    // message.warn(<ProviderStore><LogoutMessage /></ProviderStore>, 0)
    store?.dispatch(setIsLoggedIn(false))
  }
  if (response.status === 403) {
    // reload重新走请求ip，设置ip访问限制为false的流程
    throttleErrorMessage(i18n.t("noPermission"));
  }
  if (!info) {
    if (response.status === 404) {
      throttleErrorMessage(i18n.t("serviceNotStarted"))
    }
    if (response.status === 405) {
      throttleErrorMessage(i18n.t("methodNotAllowed"))
    }
    if (response.status === 408) {
      throttleErrorMessage(i18n.t("requestTimeout"))
    }
  } else if ([404, 405, 408].includes(response.status)) {
    throttleErrorMessage(info);
  }
  if (response.status === 413) {
    const message = response?.statusText ? `${response?.status}, ${response?.statusText}` : response.status
    throttleErrorMessage(`${message}`)
  }
  if (response.status >= 500) {
    if (response.status === 502) {
      throttleErrorMessage(i18n.t("gatewayError"))
    } else if (response.status === 503) {
      throttleErrorMessage(i18n.t("serviceUnavailable"))
    } else if (response.status === 504) {
      throttleErrorMessage(i18n.t("gatewayTimeout"))
    } else {
      throttleErrorMessage(i18n.t("serviceException"))
    }
  }
  throw new Error(`${response.status}, ${response.statusText}`)
}

const handleDataError = ({
  resMsg,
  resCode,
}: {
  resMsg: string
  resCode: number
}) => {
  if (resCode !== 10000) {
    const msg = resCode === RPC_ERROR_CODE ? RPC_ERROR_MESSAGE : resMsg
    if (msg === 'Please perform two-factor authentication') {
      throttleWarnMessage(msg)
    } else if(resCode === SDT_NODE_ERROR_CODE) {
      throttleErrorMessageSdt(msg)
    } else {
      throttleErrorMessage(msg)
    }
    throw new Error(msg)
  }
}


/**
 * 处理过 http 状态码报错的 fetch 请求
 */
export const fetchWithStatusHandler = async (
  ...args: Parameters<typeof fetch>
): Promise<Response> => {
  try {
    const response:any = await fetch(...args)
    // 系统设置-授权信息，证书的上传、更新等，要额外安装许可证服务才可使用
    // 未安装许可证服务-解决方式：调用接口报错状态码[404, 405, 408]，报未安装许可证服务
    const urlStr = {...args}?.[0]?.toString()
    if (urlStr?.startsWith("/license") && !urlStr?.includes("/update")) {
      handleHttpError(response, i18n.t("licenseNotInstalled"))
    } else {
      handleHttpError(response)
    }
    return response
  } catch (e) {
    if (e instanceof Error) {
      return Promise.reject(e.message)
    }
    return Promise.reject(e)
  }
}


/**
 * 处理过 http 状态码报错的 fetch 请求
 */
export const formFetchWithStatusHandler = async (
  ...args: Parameters<typeof fetch>
): Promise<Response> => {
  try {
    const response = await fetch(...args)
    handleHttpError(response)
    return response
  } catch (e: any) {
    if (e instanceof Error) {
      return Promise.reject(e.message)
    }
    // 上传失败报错提示 
    throttleErrorMessage(i18n.t("uploadFailed"))
    return Promise.reject(e)
  }
}

export const requestInitJSON = (params?: object | string) => {
  const headers = { 'Content-Type': 'application/json' }
  let body: string | undefined
  if (typeof params === 'string') {
    body = params
  } else {
    body = JSON.stringify(params)
  }
  return { headers, body }
}

export const requestInitFormData = (params?: Record<any, any>) => {
  const formData = new FormData()
  if (params) {
    Object.keys(params).forEach((key) => {
      if(Array.isArray(params[key])) {
        params[key].forEach((item: any) => {
          formData.append(key, item)
        })
      }else{
        formData.append(key, params[key])
      }
    })
  }
  return { body: formData }
}

/**
 * read response as json，handle error in json
 */
export const readResponseAsJSON = async (response: Response) => {
  try {
    const { data, ...rest } = await response.json()
    handleDataError(rest)
    return data
  } catch (e) {
    if (e instanceof Error) {
      return Promise.reject(e.message)
    }
    return Promise.reject(e)
  }
}

/**
 * handle bigint in json
 */
export const readResponseAsJSONBig = async (response: Response) => {
  try {
    const str = await response.text()
    const { data, ...rest } = JSONBig.parse(str)
    handleDataError(rest)
    return data
  } catch (e) {
    if (e instanceof Error) {
      return Promise.reject(e.message)
    }
    return Promise.reject(e)
  }
}

/**
 * get filename from response
 */
const getFilenameFromResponse = (response: Response) => {
  let filename = ''
  const disposition = response.headers.get('Content-Disposition')
  if (disposition && disposition.indexOf('attachment') !== -1) {
    // https://stackoverflow.com/questions/23054475/javascript-regex-for-extracting-filename-from-content-disposition-header
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(disposition)
    if (matches?.[1]) {
      filename = matches[1]?.replace(/['"]/g, '')
    }
  }
  try {
    filename = decodeURIComponent(filename)
  } catch (e) {
    console.error(e)
  }
  return filename
}

/**
 * handle download response
 */
export const downloadResponse = async (response: Response, fileName?: string) => {
  try {
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const filename = getFilenameFromResponse(response)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName ?? filename
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    URL.revokeObjectURL(link.href)
    link.remove()
  } catch (e) {
    if (e instanceof Error) {
      return Promise.reject(e.message)
    }
    return Promise.reject(e)
  }
}

// 各种类型文件对应文件的 Content-Type 映射 -- 需要维护
const FILE_EXTENSION_EUM: { [key: string]: string[] } = {
  'text/csv': ['.csv'],
}

// https协议或localhost环境下保存会打开文件夹选择保存位置，http下则会直接走下载方法
export const downloadopenFileResponse = async (response: Response, fileName?: string) => {
  // 判断当前环境是否可以使用showSaveFilePicker
  if (typeof (window as any)?.showSaveFilePicker === 'function') {
    const filename = getFilenameFromResponse(response)
    const blob = await response.blob()
    const contentType = response.headers.get('Content-Type') ?? 'text/csv'
    const fileExtension: string[] = FILE_EXTENSION_EUM[contentType]
    try {
      const options = {
        suggestedName: fileName ?? filename, // 文件名称
        excludeAcceptAllOption: true, // 是否排除“所有文件”选项
        startIn: "downloads",
        types: [
          {
            description: contentType,
            accept: {
              [contentType]: fileExtension,
            },
          },
        ],
      };
      const handle = await (window as any)?.showSaveFilePicker(options);
      const writable = await handle.createWritable();
      await writable.write(blob);
      await writable.close();
      message.success(String(i18n.t("fileSavedSuccessfully")));
    } catch (error) {
      if (error instanceof Error) {
        return Promise.reject(error)
      }
      return Promise.reject(new Error(error as string))
    }
  }
  else {
    return downloadResponse(response, fileName)
  }
}

// 根据操作系统参数生成一个设备ID
export const generateDeviceId = () => {
  const parser = new UAParser()
  let deviceInfo = parser.getResult()
  deviceInfo.ua = String(window.screen.width)
  deviceInfo.browser = { name: '', version: '', major: '' }
  const deviceId = new SparkMD5()
  deviceId.append(JSON.stringify(deviceInfo))
  return deviceId.end()
}