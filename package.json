{"name": "cloud-query", "version": "0.1.0", "private": true, "dependencies": {"@ag-grid-community/client-side-row-model": "^29.3.5", "@ag-grid-community/core": "^29.3.5", "@ag-grid-community/infinite-row-model": "^29.3.5", "@ag-grid-community/react": "^29.3.5", "@ahooksjs/use-request": "^2.8.10", "@ant-design/icons": "~4.8.0", "@antv/data-set": "^0.11.8", "@antv/g2": "^4.1.0", "@bintoolsfe/bintools_ui": "^0.0.5-nofield", "@craco/craco": "^6.1.2", "@loadable/component": "^5.14.1", "@pansy/react-watermark": "^3.1.14", "@reduxjs/toolkit": "^1.6.2", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/file-saver": "^2.0.7", "@types/jest": "^24.0.0", "@types/lodash": "^4.14.161", "@types/node": "^12.0.0", "@types/react": "^18.2.0", "@types/react-color": "^3.0.12", "@types/react-dom": "^18.2.0", "@types/react-highlight-words": "^0.16.1", "@types/react-joyride": "^2.0.5", "@types/react-redux": "^7.1.7", "@types/react-resizable": "^1.7.2", "@types/react-router-dom": "^5.1.5", "@types/socket.io-client": "^1.4.36", "@types/uuid": "^8.3.0", "@wecom/jssdk": "^1.4.3", "ag-grid-community": "^29.3.5", "antd": "~4.24.0", "antd-image-cropper": "^0.4.0", "antd-img-crop": "^4.6.0", "antlr4": "^4.13.1-patch-1", "antlr4ts": "^0.5.0-alpha.3", "bpmn-js": "^6.3.4", "bpmn-js-properties-panel": "^0.33.2", "bpmnlint": "^6.4.0", "caniuse-lite": "^1.0.30001207", "classnames": "^2.2.6", "copy-to-clipboard": "^3.3.1", "craco-antd": "^1.19.0", "crypto-js": "^4.0.0", "dayjs": "^1.8.36", "echarts": "^5.5.0", "fetch-jsonp": "^1.1.3", "file-saver": "^2.0.5", "history": "4.10.1", "i18next": "^23.16.8", "idb": "^7.1.1", "immutability-helper": "^3.1.1", "jquery": "^3.7.1", "jsencrypt": "^3.3.1", "json-bigint": "^1.0.0", "json-formatter-js": "^2.3.4", "jszip": "^3.10.1", "lint-staged": "^10.5.4", "localforage": "^1.10.0", "lodash": "^4.17.20", "monaco-editor": "^0.21.1", "monaco-editor-webpack-plugin": "^2.0.0", "monaco-languageclient": "0.14.0-fix.2", "qnn-react-cron": "^2.0.1", "rc-segmented": "^2.1.2", "react": "^18.2.0", "react-color": "^2.19.3", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^18.2.0", "react-highlight-words": "^0.16.0", "react-i18next": "^14.1.3", "react-joyride": "^2.5.3", "react-json-view": "^1.21.3", "react-redux": "^7.2.1", "react-resizable": "^1.11.0", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "react-use": "17.2.4", "react-virtualized-auto-sizer": "^1.0.5", "react-window": "^1.8.6", "react-window-infinite-loader": "^1.0.7", "reconnecting-websocket": "^4.4.0", "redux-persist": "^6.0.0", "sass": "^1.26.11", "select2": "^4.1.0-rc.0", "sm-crypto": "^0.3.12", "socket.io-client": "^1.7.3", "spark-md5": "^3.0.2", "sql-formatter": "^0.1.1", "ua-parser-js": "^0.7.37", "uuid": "^8.3.2", "vscode-ws-jsonrpc": "^0.2.0", "xlsx": "^0.16.9"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "commit": "cz-customizable", "prepare": "husky install", "lint": "npx eslint . --fix --ext .js,.jsx,.ts,.tsx --max-warnings 0 --max-errors 0"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@types/classnames": "^2.2.10", "@types/jquery": "^3.5.29", "@types/json-bigint": "^1.0.1", "@types/loadable__component": "^5.13.3", "@types/mockjs": "^1.0.7", "@types/react-virtualized-auto-sizer": "^1.0.0", "@types/react-window": "^1.8.2", "@types/react-window-infinite-loader": "^1.0.3", "@types/sm-crypto": "^0.3.0", "@types/spark-md5": "^3.0.2", "@types/ua-parser-js": "^0.7.36", "@types/xlsx": "^0.0.36", "commitlint-config-cz": "^0.13.2", "cz-customizable": "^6.3.0", "husky": "^6.0.0", "mockjs": "^1.1.0", "resize-observer-polyfill": "^1.5.1", "speed-measure-webpack-plugin": "^1.3.3", "typescript": "^4.7.4", "webpack-bundle-analyzer": "^3.9.0"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": []}}